
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const Footer = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-black border-t border-white/10">
      <div className="container mx-auto px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="font-montserrat font-bold text-2xl text-white">
              <span className="text-primary">M</span>BI
            </div>
            <p className="font-poppins text-gray-400 text-sm">
              Transforming ideas into digital reality. Empowering businesses with innovative solutions.
            </p>
            <div className="flex space-x-3">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-primary p-2">
                📧
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-primary p-2">
                💼
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-primary p-2">
                🐦
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-primary p-2">
                📷
              </Button>
            </div>
          </div>
          
          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-montserrat font-semibold text-white">Quick Links</h3>
            <ul className="space-y-2">
              {[
                { label: 'Home', id: 'home' },
                { label: 'About', id: 'about' },
                { label: 'Services', id: 'services' },
                { label: 'Portfolio', id: 'portfolio' }
              ].map((link, index) => (
                <li key={index}>
                  <button 
                    onClick={() => scrollToSection(link.id)}
                    className="font-poppins text-gray-400 hover:text-primary transition-colors text-sm"
                  >
                    {link.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Services */}
          <div className="space-y-4">
            <h3 className="font-montserrat font-semibold text-white">Services</h3>
            <ul className="space-y-2">
              {[
                'MVP Development',
                'Custom Web Solutions',
                'Landing Pages',
                'SaaS Platforms',
                'Digital Strategy'
              ].map((service, index) => (
                <li key={index} className="font-poppins text-gray-400 text-sm">
                  {service}
                </li>
              ))}
            </ul>
          </div>
          
          {/* Newsletter */}
          <div className="space-y-4">
            <h3 className="font-montserrat font-semibold text-white">Stay Updated</h3>
            <p className="font-poppins text-gray-400 text-sm">
              Subscribe to our newsletter for the latest insights and updates.
            </p>
            <div className="flex space-x-2">
              <Input 
                placeholder="Your email"
                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:border-primary text-sm"
              />
              <Button 
                size="sm"
                className="bg-primary hover:bg-primary/80 text-white px-4"
              >
                Subscribe
              </Button>
            </div>
          </div>
        </div>
        
        <div className="border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="font-poppins text-gray-400 text-sm">
            © 2024 Millennial Business Innovations. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <button className="font-poppins text-gray-400 hover:text-primary text-sm transition-colors">
              Privacy Policy
            </button>
            <button className="font-poppins text-gray-400 hover:text-primary text-sm transition-colors">
              Terms of Service
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
