
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const ServicesSection = () => {
  const services = [
    {
      icon: '⚡',
      title: 'MVP Development',
      description: 'Rapidly prototype and validate your ideas with a Minimum Viable Product (MVP). We help you launch quickly and iterate based on real user feedback.',
      features: ['Rapid prototyping', 'User validation', 'Iterative development', 'Market testing']
    },
    {
      icon: '🌐',
      title: 'Custom Web Solutions',
      description: 'Build bespoke websites tailored to your unique business needs. From design to development, we handle it all.',
      features: ['Custom design', 'Responsive layouts', 'Modern frameworks', 'Performance optimized']
    },
    {
      icon: '🎯',
      title: 'Landing Pages',
      description: 'Create high-converting landing pages that capture leads and drive conversions. Our designs are optimized for performance and engagement.',
      features: ['Conversion focused', 'A/B testing', 'Mobile optimized', 'Fast loading']
    },
    {
      icon: '☁️',
      title: 'SaaS Platforms',
      description: 'Develop scalable Software-as-a-Service (SaaS) solutions that meet your business requirements. We build robust, secure, and user-friendly platforms.',
      features: ['Scalable architecture', 'Security first', 'User management', 'API integration']
    },
    {
      icon: '📊',
      title: 'Digital Strategy Consulting',
      description: 'Need guidance on how to leverage technology for growth? Our experts provide strategic consulting to help you navigate the digital landscape.',
      features: ['Strategic planning', 'Technology roadmap', 'Growth analysis', 'Best practices']
    },
    {
      icon: '🔧',
      title: 'Technical Support',
      description: 'Ongoing technical support and maintenance to ensure your digital solutions continue to perform at their best.',
      features: ['24/7 monitoring', 'Regular updates', 'Bug fixes', 'Performance optimization']
    }
  ];

  return (
    <section id="services" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="font-montserrat font-bold text-4xl md:text-5xl text-white mb-6">
            Our <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Services</span>
          </h2>
          <p className="font-poppins text-xl text-gray-300 max-w-3xl mx-auto">
            We offer a comprehensive range of digital services to help your business thrive in the modern marketplace.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className="bg-white/5 border border-white/10 backdrop-blur-lg hover:bg-white/10 transition-all duration-300 hover:transform hover:scale-105 group"
            >
              <CardHeader className="text-center">
                <div className="text-6xl mb-4 group-hover:animate-float">{service.icon}</div>
                <CardTitle className="font-montserrat text-2xl text-white group-hover:text-primary transition-colors">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="font-poppins text-gray-300 text-center">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-400">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <Button 
                  variant="ghost" 
                  className="w-full text-primary border border-primary/30 hover:bg-primary/10 hover:border-primary/50 transition-all duration-300"
                >
                  Learn More
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
