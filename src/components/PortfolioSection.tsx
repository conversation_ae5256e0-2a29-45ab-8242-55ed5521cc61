
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const PortfolioSection = () => {
  const projects = [
    {
      title: 'XYZ Startup MVP',
      category: 'MVP Development',
      description: 'Helped XYZ startup develop an MVP within 4 weeks, resulting in a 30% increase in user engagement and successful seed funding.',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop',
      tech: ['React', 'Node.js', 'MongoDB'],
      metrics: '30% increase in engagement'
    },
    {
      title: 'E-commerce Platform',
      category: 'Custom Web Solution',
      description: 'Built a fully responsive e-commerce platform with integrated payment systems and inventory management.',
      image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=500&h=300&fit=crop',
      tech: ['Next.js', 'Stripe', 'PostgreSQL'],
      metrics: '150% sales increase'
    },
    {
      title: 'SaaS Analytics Dashboard',
      category: 'SaaS Platform',
      description: 'Developed a comprehensive analytics dashboard for a B2B SaaS company, handling millions of data points.',
      image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=500&h=300&fit=crop',
      tech: ['Vue.js', 'Python', 'AWS'],
      metrics: '50% faster insights'
    },
    {
      title: 'Marketing Landing Pages',
      category: 'Landing Pages',
      description: 'Created high-converting landing pages for various marketing campaigns with A/B testing optimization.',
      image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=500&h=300&fit=crop',
      tech: ['React', 'Tailwind', 'Vercel'],
      metrics: '85% conversion rate'
    },
    {
      title: 'Mobile-First Web App',
      category: 'Custom Web Solution',
      description: 'Designed and developed a mobile-first progressive web application with offline capabilities.',
      image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=500&h=300&fit=crop',
      tech: ['PWA', 'TypeScript', 'Firebase'],
      metrics: '95% mobile satisfaction'
    },
    {
      title: 'Enterprise Dashboard',
      category: 'SaaS Platform',
      description: 'Built a comprehensive enterprise dashboard with role-based access control and real-time data visualization.',
      image: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=500&h=300&fit=crop',
      tech: ['Angular', 'D3.js', 'Docker'],
      metrics: '40% efficiency gain'
    }
  ];

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="font-montserrat font-bold text-4xl md:text-5xl text-white mb-6">
            Our <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Portfolio</span>
          </h2>
          <p className="font-poppins text-xl text-gray-300 max-w-3xl mx-auto">
            Discover some of our recent projects and the impact we've made for our clients.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card 
              key={index} 
              className="bg-white/5 border border-white/10 backdrop-blur-lg hover:bg-white/10 transition-all duration-300 overflow-hidden group hover:transform hover:scale-105"
            >
              <div className="relative overflow-hidden">
                <img 
                  src={project.image} 
                  alt={project.title}
                  className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <span className="bg-primary/80 text-white px-3 py-1 rounded-full text-sm font-poppins">
                    {project.category}
                  </span>
                </div>
              </div>
              
              <CardContent className="p-6 space-y-4">
                <h3 className="font-montserrat font-semibold text-xl text-white group-hover:text-primary transition-colors">
                  {project.title}
                </h3>
                <p className="font-poppins text-gray-300 text-sm">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech, idx) => (
                    <span 
                      key={idx} 
                      className="bg-accent/20 text-accent px-2 py-1 rounded text-xs font-poppins"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-white/10">
                  <span className="text-secondary text-sm font-poppins font-semibold">
                    {project.metrics}
                  </span>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="text-primary hover:text-primary/80 hover:bg-primary/10"
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <Button 
            className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-8 py-3 rounded-full"
          >
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
